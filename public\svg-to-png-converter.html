<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loop Logo SVG to PNG Converter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #059669;
            text-align: center;
        }
        .logo-preview {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .size-option {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .size-option h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
        button {
            background: #059669;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #047857;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .current-logo {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Loop Logo SVG to PNG Converter</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>The correct logo (green background, white rim) is shown below</li>
                <li>Click "Generate PNG" for each size needed</li>
                <li>Right-click the generated image and "Save As" with the correct filename</li>
                <li>Replace the existing PNG files in your /public directory</li>
            </ol>
        </div>

        <div class="logo-preview">
            <h2>Logo Preview</h2>
            <div class="current-logo">
                <img src="/wheel-logo.svg" alt="Original Logo" style="width: 64px; height: 64px;">
                <p><strong>📄 Original SVG</strong><br>Green circle, transparent background</p>
            </div>
            <div class="current-logo">
                <img src="/wheel-logo-mobile.svg" alt="Mobile Logo" style="width: 64px; height: 64px;">
                <p><strong>✅ Mobile App Icon</strong><br>Full green background, white wheel</p>
            </div>
            <div class="current-logo">
                <img src="/wheel-logo-alt.svg" alt="Wrong Logo" style="width: 64px; height: 64px;">
                <p><strong>❌ Wrong Version</strong><br>White background, green rim</p>
            </div>
        </div>

        <div class="size-option">
            <h3>📱 android-chrome-192x192.png</h3>
            <canvas id="canvas192" width="192" height="192"></canvas>
            <button onclick="generatePNG(192, 'android-chrome-192x192.png')">Generate 192x192 PNG</button>
        </div>

        <div class="size-option">
            <h3>📱 android-chrome-512x512.png</h3>
            <canvas id="canvas512" width="512" height="512"></canvas>
            <button onclick="generatePNG(512, 'android-chrome-512x512.png')">Generate 512x512 PNG</button>
        </div>

        <div class="size-option">
            <h3>🍎 apple-touch-icon.png</h3>
            <canvas id="canvas180" width="180" height="180"></canvas>
            <button onclick="generatePNG(180, 'apple-touch-icon.png')">Generate 180x180 PNG</button>
        </div>

        <div class="size-option">
            <h3>🔖 favicon-32x32.png</h3>
            <canvas id="canvas32" width="32" height="32"></canvas>
            <button onclick="generatePNG(32, 'favicon-32x32.png')">Generate 32x32 PNG</button>
        </div>

        <div class="size-option">
            <h3>🔖 favicon-16x16.png</h3>
            <canvas id="canvas16" width="16" height="16"></canvas>
            <button onclick="generatePNG(16, 'favicon-16x16.png')">Generate 16x16 PNG</button>
        </div>
    </div>

    <script>
        // SVG content for the correct logo (green square background, all-white wheel)
        const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Full green square background -->
  <rect x="0" y="0" width="24" height="24" fill="#059669" />
  <!-- Outer wheel circle with white rim (no fill - green shows through) -->
  <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" fill="none" />
  <!-- Inner hub circle with white stroke and hole (green shows through) -->
  <circle cx="12" cy="12" r="2.5" stroke="white" stroke-width="1.5" fill="none" />
  <!-- All wheel spokes are white - touching rim exactly -->
  <path d="M12 9.5V2" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M12 22V14.5" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M9.5 12H2" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M22 12H14.5" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M10.1 10.1L4.9 4.9" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M19.1 19.1L13.9 13.9" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M10.1 13.9L4.9 19.1" stroke="white" stroke-width="1.5" stroke-linecap="round" />
  <path d="M19.1 4.9L13.9 10.1" stroke="white" stroke-width="1.5" stroke-linecap="round" />
</svg>`;

        function generatePNG(size, filename) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Create image from SVG
            const img = new Image();
            const svgBlob = new Blob([svgContent], {type: 'image/svg+xml'});
            const url = URL.createObjectURL(svgBlob);
            
            img.onload = function() {
                ctx.drawImage(img, 0, 0, size, size);
                URL.revokeObjectURL(url);
                
                // Convert to downloadable link
                canvas.toBlob(function(blob) {
                    const link = document.createElement('a');
                    link.download = filename;
                    link.href = URL.createObjectURL(blob);
                    link.click();
                    URL.revokeObjectURL(link.href);
                }, 'image/png');
            };
            
            img.src = url;
        }
    </script>
</body>
</html>
