'use client'

import { useState, useEffect } from 'react'
import FilterSidebar from './filter-sidebar'
import FilterCompact from './filter-compact'

type SearchFiltersProps = {
  businessType: string
  onFiltersChange: (filters: string[]) => void
  onMaxDeliveryTimeChange: (time: number) => void
  onMaxPrepTimeChange: (time: number) => void
  onTypeChange?: (type: string) => void
}

export default function SearchFilters({
  businessType,
  onFiltersChange,
  onMaxDeliveryTimeChange,
  onMaxPrepTimeChange,
  onTypeChange
}: SearchFiltersProps) {
  const [activeFilters, setActiveFilters] = useState<string[]>([])
  const [maxDeliveryTime, setMaxDeliveryTime] = useState<number>(30)
  const [maxPrepTime, setMaxPrepTime] = useState<number>(30)
  const [isMobile, setIsMobile] = useState<boolean>(false)

  // Check if we're on mobile/tablet
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    // Initial check
    checkIfMobile()

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile)
  }, [])

  // Handle filter toggle
  const handleFilterChange = (filterId: string) => {
    setActiveFilters(prev => {
      const newFilters = prev.includes(filterId)
        ? prev.filter(id => id !== filterId)
        : [...prev, filterId]

      return newFilters
    })
  }

  // Handle clear all filters
  const handleClearFilters = () => {
    setActiveFilters([])
    setMaxDeliveryTime(30)
    setMaxPrepTime(30)
  }

  // Notify parent component when activeFilters changes
  useEffect(() => {
    onFiltersChange(activeFilters)
  }, [activeFilters, onFiltersChange])



  // Handle delivery time change
  const handleMaxDeliveryTimeChange = (time: number) => {
    setMaxDeliveryTime(time)
  }

  // Handle prep time change
  const handleMaxPrepTimeChange = (time: number) => {
    setMaxPrepTime(time)
  }



  // Notify parent component when maxDeliveryTime changes
  useEffect(() => {
    onMaxDeliveryTimeChange(maxDeliveryTime)
  }, [maxDeliveryTime, onMaxDeliveryTimeChange])

  // Notify parent component when maxPrepTime changes
  useEffect(() => {
    onMaxPrepTimeChange(maxPrepTime)
  }, [maxPrepTime, onMaxPrepTimeChange])

  return (
    <>
      {/* Mobile/Tablet View */}
      <div className={`lg:hidden ${isMobile ? 'block' : 'hidden'}`}>
        <FilterCompact
          businessType={businessType}
          activeFilters={activeFilters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          maxDeliveryTime={maxDeliveryTime}
          onMaxDeliveryTimeChange={handleMaxDeliveryTimeChange}
          maxPrepTime={maxPrepTime}
          onMaxPrepTimeChange={handleMaxPrepTimeChange}
        />
      </div>

      {/* Desktop View */}
      <div className={`hidden lg:block ${!isMobile ? 'block' : 'hidden'}`}>
        <FilterSidebar
          businessType={businessType}
          activeFilters={activeFilters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
          maxDeliveryTime={maxDeliveryTime}
          onMaxDeliveryTimeChange={handleMaxDeliveryTimeChange}
          maxPrepTime={maxPrepTime}
          onMaxPrepTimeChange={handleMaxPrepTimeChange}
          onTypeChange={onTypeChange}
        />
      </div>
    </>
  )
}
