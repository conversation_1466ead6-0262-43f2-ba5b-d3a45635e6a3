// Get Supabase URL and keys from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Helper function to make API calls to Supabase
async function fetchFromSupabase(path: string, params: Record<string, string> = {}) {
  try {

    // Build query string manually to handle special Supabase query parameters
    let queryParts: string[] = [];

    for (const [key, value] of Object.entries(params)) {
      // Handle special cases for Supabase
      if (key === 'select') {
        queryParts.push(`select=${encodeURIComponent(value)}`);
      } else if (key === 'order') {
        queryParts.push(`order=${encodeURIComponent(value)}`);
      } else if (key === 'in') {
        // For 'in' operator, we need to handle it specially
        const [column, values] = value.split(':');
        queryParts.push(`${encodeURIComponent(column)}=in.(${encodeURIComponent(values)})`);
      } else if (key === 'eq') {
        // For 'eq' operator, we need to handle it specially
        const [column, val] = value.split(':');
        queryParts.push(`${encodeURIComponent(column)}=eq.${encodeURIComponent(val)}`);
      } else {
        // For other parameters, use the value as is
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }

    // Construct the final query string
    const queryString = queryParts.length > 0 ? '?' + queryParts.join('&') : '';

    // Construct the full URL
    const url = `${supabaseUrl}/rest/v1/${path}${queryString}`;

    // Make API call
    const response = await fetch(url, {
      headers: {
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

// Types
export type AttributeType = {
  attribute_type: string;
  attribute_value: string;
  count?: number;
};

export type AttributeGroup = {
  type: string;
  displayName: string;
  values: string[];
  count?: number;
};

// Get all unique attribute types for a business type
export async function getAttributeTypesForBusinessType(businessType: string): Promise<string[]> {
  try {
    // Handle "all" business type case
    if (businessType === 'all') {
      // For "all", we'll get attribute types from all business types
      const attributes = await fetchFromSupabase('business_attributes', {
        'select': 'attribute_type',
        'order': 'attribute_type'
      });

      // Extract unique attribute types
      const uniqueTypes = [...new Set(attributes?.map((a: any) => a.attribute_type) || [])];
      return uniqueTypes;
    }

    // Get business type ID
    const businessTypeData = await fetchFromSupabase('business_types', {
      'select': 'id',
      'eq': `slug:${businessType}`
    });

    if (!businessTypeData || businessTypeData.length === 0) {
      return [];
    }

    const businessTypeId = businessTypeData[0].id;

    // Get all businesses of this type
    const businesses = await fetchFromSupabase('businesses', {
      'select': 'id',
      'eq': `business_type_id:${businessTypeId}`
    });

    if (!businesses || businesses.length === 0) {
      return [];
    }

    // Get all attribute types for these businesses
    const businessIds = businesses.map((b: any) => b.id).join(',');
    const attributes = await fetchFromSupabase('business_attributes', {
      'select': 'attribute_type',
      'in': `business_id:${businessIds}`,
      'order': 'attribute_type'
    });

    // Extract unique attribute types
    const uniqueTypes = [...new Set(attributes?.map((a: any) => a.attribute_type) || [])];
    return uniqueTypes;
  } catch (error) {
    return [];
  }
}

// Get all attributes for a business type using API endpoint
export async function getAttributesForBusinessType(businessType: string): Promise<AttributeGroup[]> {
  try {
    console.log(`Fetching attributes for business type: ${businessType}`);

    // Use the API endpoint for better performance and consistency
    const response = await fetch(`/api/attributes-by-type?businessType=${businessType}`);

    if (!response.ok) {
      console.error(`API call failed: ${response.status} ${response.statusText}`);
      return getDefaultAttributeGroups(businessType);
    }

    const data = await response.json();
    const { attributes } = data;

    // Use the deduplicated attributes from the API
    if (!attributes || attributes.length === 0) {
      console.log(`No attributes found for business type ${businessType}, using defaults`);
      return getDefaultAttributeGroups(businessType);
    }

    // Convert the deduplicated API format to AttributeGroup format
    // Exclude prep_time_minutes as it's handled by the Max. Prep. Time filter
    const attributeGroups: AttributeGroup[] = attributes
      .filter((attr: any) => attr.attribute_type !== 'prep_time_minutes')
      .map((attr: any) => ({
        type: attr.attribute_type,
        displayName: formatAttributeTypeName(attr.attribute_type),
        values: attr.values.map((value: string) => formatAttributeValue(value))
      }));

    console.log(`Found ${attributeGroups.length} attribute groups for business type ${businessType}`);
    return attributeGroups;

  } catch (error) {
    console.error('Error fetching attributes:', error);
    return getDefaultAttributeGroups(businessType); // Return default attributes as fallback
  }
}

// Get all product attributes for a business type
export async function getProductAttributesForBusinessType(businessType: string): Promise<AttributeGroup[]> {
  try {
    console.log(`Fetching product attributes for business type: ${businessType}`);

    // Use the API endpoint for consistency
    const response = await fetch(`/api/attributes-by-type?businessType=${businessType}`);

    if (!response.ok) {
      console.error(`API call failed: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    const { productAttributes } = data;

    if (!productAttributes || productAttributes.length === 0) {
      console.log(`No product attributes found for business type ${businessType}`);
      return [];
    }

    // Group attributes by type, excluding prep_time_minutes and formatting values
    const attributeGroups: AttributeGroup[] = [];
    productAttributes
      .filter((attr: any) => attr.attribute_type !== 'prep_time_minutes')
      .forEach((attr: any) => {
        const formattedValue = formatAttributeValue(attr.attribute_value);
        const existingGroup = attributeGroups.find(g => g.type === attr.attribute_type);
        if (existingGroup) {
          if (!existingGroup.values.includes(formattedValue)) {
            existingGroup.values.push(formattedValue);
          }
        } else {
          attributeGroups.push({
            type: attr.attribute_type,
            displayName: formatAttributeTypeName(attr.attribute_type),
            values: [formattedValue]
          });
        }
      });

    console.log(`Found ${attributeGroups.length} product attribute groups for business type ${businessType}`);
    return attributeGroups;

  } catch (error) {
    console.error('Error fetching product attributes:', error);
    return []; // Return empty array as fallback for product attributes
  }
}

// Helper function to format attribute type names for display
export function formatAttributeTypeName(type: string): string {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format attribute values for display (sentence case)
export function formatAttributeValue(value: string): string {
  return value
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

// Helper function to create filter ID from attribute
export function createAttributeFilterId(type: string, value: string): string {
  return `attr_${type}_${value.toLowerCase().replace(/\s+/g, '_')}`;
}

// Helper function to parse attribute filter ID
export function parseAttributeFilterId(filterId: string): { type: string, value: string } | null {
  if (!filterId.startsWith('attr_')) return null;

  const parts = filterId.substring(5).split('_');
  if (parts.length < 2) return null;

  const type = parts[0];
  const value = parts.slice(1).join('_');

  return { type, value };
}

// Get default attribute groups based on business type
export function getDefaultAttributeGroups(businessType: string): AttributeGroup[] {
  switch (businessType) {
    case 'restaurant':
      return [
        {
          type: 'cuisine',
          displayName: 'Cuisine',
          values: [
            'Chinese', 'Italian', 'Pizza', 'Burgers', 'Casual Dining',
            'Street Food', 'Breakfast & Lunch', 'Indian', 'American',
            'Thai', 'Japanese', 'Spanish', 'Malaysian', 'Chip Shop',
            'Fresh Fish', 'Spicy Food', 'Seafood'
          ]
        },
        {
          type: 'dietary',
          displayName: 'Dietary Options',
          values: ['Vegetarian', 'Vegan', 'Gluten Free', 'Halal', 'Dairy Free']
        },
        {
          type: 'meal_type',
          displayName: 'Meal Type',
          values: ['Breakfast', 'Lunch', 'Dinner', 'Brunch', 'Late Night', 'Snacks']
        },
        {
          type: 'delivery_option',
          displayName: 'Service Options',
          values: ['Pickup', 'Delivery']
        },
        {
          type: 'service_style',
          displayName: 'Service Style',
          values: ['Fast Food', 'Casual Dining', 'Fine Dining', 'Takeaway Only']
        }
      ];
    case 'shop':
      return [
        {
          type: 'store_type',
          displayName: 'Store Type',
          values: ['Grocery', 'Convenience', 'Supermarket', 'Electronics', 'Gifts', 'Specialty Store']
        },
        {
          type: 'product_category',
          displayName: 'Product Category',
          values: ['Fresh Produce', 'Dairy & Eggs', 'Bakery', 'Household', 'Electronics', 'Clothing']
        },
        {
          type: 'store_size',
          displayName: 'Store Size',
          values: ['Local Shop', 'Supermarket', 'Specialty Store']
        },
        {
          type: 'delivery_option',
          displayName: 'Service Options',
          values: ['Pickup', 'Delivery']
        }
      ];
    case 'pharmacy':
      return [
        {
          type: 'pharmacy_type',
          displayName: 'Pharmacy Type',
          values: ['Prescription', 'Over-the-counter', 'Compounding', 'Veterinary']
        },
        {
          type: 'pharmacy_services',
          displayName: 'Services',
          values: ['Health Consultation', 'Blood Pressure Check', 'Vaccinations', 'Medicine Review']
        },
        {
          type: 'specialties',
          displayName: 'Specialties',
          values: ['General', 'Compounding', 'Veterinary', 'Travel Health']
        }
      ];
    case 'cafe':
      return [
        {
          type: 'cafe_type',
          displayName: 'Cafe Type',
          values: ['Coffee Shop', 'Tea House', 'Bakery Cafe', 'Sandwich Bar']
        },
        {
          type: 'cafe_specialties',
          displayName: 'Specialties',
          values: ['Artisan Coffee', 'Fresh Pastries', 'Light Meals', 'Breakfast', 'Desserts']
        },
        {
          type: 'atmosphere',
          displayName: 'Atmosphere',
          values: ['Quiet Study', 'Social', 'Outdoor Seating', 'Family Friendly']
        }
      ];
    case 'errand':
      return [
        {
          type: 'errand_type',
          displayName: 'Service Type',
          values: ['Shopping', 'Delivery', 'Tasks', 'Personal Errands']
        },
        {
          type: 'availability',
          displayName: 'Availability',
          values: ['Same Day', 'Scheduled', 'Emergency', 'Regular Service']
        },
        {
          type: 'specialization',
          displayName: 'Specialization',
          values: ['Grocery Shopping', 'Document Delivery', 'Pet Care', 'Elderly Assistance']
        }
      ];
    case 'all':
      // For "all" business types, return a combined set of the most common filters
      return [
        {
          type: 'cuisine',
          displayName: 'Cuisine',
          values: ['Chinese', 'Italian', 'Pizza', 'Burgers', 'Indian', 'Thai']
        },
        {
          type: 'store_type',
          displayName: 'Store Type',
          values: ['Grocery', 'Convenience', 'Pharmacy', 'Electronics']
        },
        {
          type: 'service_type',
          displayName: 'Service Type',
          values: ['Food Delivery', 'Shopping', 'Pharmacy', 'Errands']
        }
      ];
    default:
      return [];
  }
}
