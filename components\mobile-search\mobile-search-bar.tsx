'use client'

import { useState } from 'react'
import { Search, SlidersHorizontal } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  She<PERSON><PERSON>ooter,
  SheetClose
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import FilterSidebar from '@/components/filters/filter-sidebar'

interface MobileSearchBarProps {
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  onSearch?: () => void
  businessType: string
  activeFilters: string[]
  onFilterChange: (filterId: string) => void
  onClearFilters: () => void
  maxDeliveryTime: number
  onMaxDeliveryTimeChange: (time: number) => void
  maxPrepTime: number
  onMaxPrepTimeChange: (time: number) => void
  onTypeChange?: (type: string) => void
  className?: string
}

export default function MobileSearchBar({
  placeholder = 'Restaurants, groceries, dishes',
  value = '',
  onChange = () => {},
  onSearch = () => {},
  businessType,
  activeFilters,
  onFilterChange,
  onClearFilters,
  maxDeliveryTime,
  onMaxDeliveryTimeChange,
  maxPrepTime,
  onMaxPrepTimeChange,
  onTypeChange,
  className
}: MobileSearchBarProps) {
  return (
    <div className={cn("px-4 py-3", className)}>
      <div className="relative flex items-center">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            type="text"
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className="pl-9 pr-9 py-2 h-10 bg-white border-gray-200 rounded-md w-full"
          />
        </div>

        <Sheet>
          <SheetTrigger asChild>
            <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
              <SlidersHorizontal className="h-4 w-4" />
            </button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[85vh] rounded-t-xl">
            <SheetHeader className="mb-4">
              <SheetTitle>Filters</SheetTitle>
            </SheetHeader>
            <div className="overflow-y-auto max-h-[calc(85vh-10rem)]">
              <FilterSidebar
                businessType={businessType}
                activeFilters={activeFilters}
                onFilterChange={onFilterChange}
                onClearFilters={onClearFilters}
                maxDeliveryTime={maxDeliveryTime}
                onMaxDeliveryTimeChange={onMaxDeliveryTimeChange}
                maxPrepTime={maxPrepTime}
                onMaxPrepTimeChange={onMaxPrepTimeChange}
                onTypeChange={onTypeChange}
              />
            </div>
            <SheetFooter className="mt-4">
              <SheetClose asChild>
                <Button className="w-full bg-[#22c55e] hover:bg-[#1ea750] text-white">
                  Apply Filters
                </Button>
              </SheetClose>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </div>
    </div>
  )
}
