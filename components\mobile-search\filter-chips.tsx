'use client'

import { X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FilterChipsProps {
  activeFilters: string[]
  onRemoveFilter: (filterId: string) => void
  className?: string
}

export default function FilterChips({
  activeFilters,
  onRemoveFilter,
  className
}: FilterChipsProps) {
  if (activeFilters.length === 0) return null
  
  return (
    <div className={cn("px-4 py-2 flex flex-wrap gap-2", className)}>
      {activeFilters.map((filter) => (
        <div
          key={filter}
          className="flex items-center bg-[#00ccbc] text-white px-3 py-1 rounded-full text-sm"
        >
          <span>{getFilterLabel(filter)}</span>
          <button
            onClick={() => onRemoveFilter(filter)}
            className="ml-1 text-white"
          >
            <X size={14} />
          </button>
        </div>
      ))}
    </div>
  )
}

// Helper function to get a human-readable label for filter IDs
function getFilterLabel(filterId: string): string {
  const filterMap: Record<string, string> = {
    'free_delivery': 'Free Delivery',
    'deals': 'Deals',
    'discounts': 'Discounts',
    'rating_4.5': '4.5+',
    'rating_4': '4.0+',
    'rating_3.5': '3.5+',
    
    // Restaurant filters
    'cuisine_italian': 'Italian',
    'cuisine_indian': 'Indian',
    'cuisine_chinese': 'Chinese',
    'cuisine_american': 'American',
    'cuisine_japanese': 'Japanese',
    'cuisine_thai': 'Thai',
    'cuisine_fresh_fish': 'Fresh Fish',
    'cuisine_spicy_food': 'Spicy Food',
    'cuisine_seafood': 'Seafood',
    'dietary_vegetarian': 'Vegetarian',
    'dietary_vegan': 'Vegan',
    'dietary_gluten_free': 'Gluten Free',

    // Delivery options
    'delivery_option_pickup': 'Pickup',
    'delivery_option_delivery': 'Delivery',

    // Shop filters
    'shop_grocery': 'Grocery',
    'shop_convenience': 'Convenience',
    'shop_homegoods': 'Home Goods',
    'shop_electronics': 'Electronics',
    
    // Pharmacy filters
    'pharmacy_prescription': 'Prescription',
    'pharmacy_otc': 'Over-the-counter',
    'pharmacy_health': 'Health & Beauty',
    
    // Cafe filters
    'cafe_coffee': 'Coffee Shop',
    'cafe_bakery': 'Bakery',
    'cafe_dessert': 'Dessert',
    
    // Lift filters
    'vehicle_car': 'Car',
    'vehicle_van': 'Van',
    'vehicle_bike': 'Bike',
    
    // Errand filters
    'errand_pickup': 'Pickup',
    'errand_dropoff': 'Dropoff',
    'errand_shopping': 'Shopping'
  }
  
  return filterMap[filterId] || filterId
}
